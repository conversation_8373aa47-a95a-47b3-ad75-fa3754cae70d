# 地址配置重构总结

## 问题描述

项目中存在多处硬编码的服务地址，主要包括：
- `http://localhost:8086` 
- `http://127.0.0.1:8086`

这些硬编码地址分布在多个文件中，不利于：
1. 环境切换（开发、测试、生产）
2. 统一管理和维护
3. 部署时的配置修改

## 解决方案

### 1. 后端配置统一管理

**新增配置项**（在各环境配置文件中）：
```ini
# 服务器基础地址
server_base_url = "http://localhost:8086"
# 上传文件访问路径前缀  
upload_url_prefix = "/uploads"
# GoView请求数据源地址
goview_data_origin_url = "http://127.0.0.1:8086"
```

**新增配置管理模块** `common/config.go`：
- 提供统一的配置读取接口
- 自动处理URL格式化
- 提供便捷的URL生成方法

### 2. 前端配置环境化

**新增环境变量配置**：
- `.env` - 开发环境配置
- `.env.production` - 生产环境配置

**修改 vite.config.ts**：
```typescript
// 修改前
target: 'http://localhost:8086',

// 修改后  
target: process.env.VITE_API_BASE_URL || 'http://localhost:8086',
```

## 修改对比

### controllers/goview.go

#### 修改前：
```go
// 硬编码地址
"bucketURL": "http://localhost:8086/uploads/"
"requestOriginUrl": "http://127.0.0.1:8086/"
"fileurl": fmt.Sprintf("http://localhost:8086/%s", filePath)
```

#### 修改后：
```go
// 从配置读取
serverConfig := common.GetGlobalServerConfig()
"bucketURL": serverConfig.GetBucketURL()
"requestOriginUrl": serverConfig.GoViewDataOriginURL
"fileurl": serverConfig.GetUploadURL(filePath)
```

### 配置文件

#### 开发环境 (conf/dev.conf)
```ini
server_base_url = "http://localhost:8086"
upload_url_prefix = "/uploads"
goview_data_origin_url = "http://127.0.0.1:8086"
```

#### 生产环境 (conf/prod.conf)
```ini
server_base_url = "https://your-production-domain.com"
upload_url_prefix = "/uploads"
goview_data_origin_url = "https://your-production-domain.com"
```

## 优势

### 1. 统一管理
- 所有地址配置集中在配置文件中
- 一处修改，全局生效

### 2. 环境隔离
- 不同环境使用不同的配置文件
- 避免环境间配置冲突

### 3. 易于部署
- 部署时只需修改配置文件
- 无需修改代码

### 4. 向后兼容
- 提供默认值，确保兼容性
- 渐进式迁移，降低风险

### 5. 类型安全
- 配置结构体提供类型检查
- 编译时发现配置错误

## 使用示例

```go
// 获取配置
config := common.GetGlobalServerConfig()

// 生成文件URL
fileURL := config.GetUploadURL("uploads/image.png")
// 结果: http://localhost:8086/uploads/image.png

// 获取存储桶URL
bucketURL := config.GetBucketURL()  
// 结果: http://localhost:8086/uploads/

// 访问原始配置
baseURL := config.BaseURL
dataOriginURL := config.GoViewDataOriginURL
```

## 测试验证

已添加单元测试 `common/config_test.go`：
- 测试配置读取功能
- 测试URL生成功能
- 测试默认值处理
- 测试路径格式化

## 后续建议

1. **逐步迁移**：继续查找项目中其他硬编码地址并迁移
2. **配置验证**：添加配置项有效性验证
3. **文档更新**：更新部署文档，说明配置项含义
4. **监控告警**：添加配置错误的监控和告警

这次重构彻底解决了地址硬编码问题，为项目的可维护性和可部署性奠定了良好基础。
