# 服务地址配置说明

## 概述

为了解决项目中硬编码地址的问题，我们引入了统一的服务地址配置管理方案。所有的服务地址现在都可以通过配置文件进行统一管理，方便在不同环境间切换。

## 配置项说明

### 配置文件位置
- `conf/app.conf` - 默认配置
- `conf/dev.conf` - 开发环境配置
- `conf/test.conf` - 测试环境配置  
- `conf/prod.conf` - 生产环境配置

### 配置项详解

```ini
# 服务器基础地址（用于生成文件访问URL等）
server_base_url = "http://localhost:8086"

# 上传文件访问路径前缀
upload_url_prefix = "/uploads"

# GoView请求数据源地址
goview_data_origin_url = "http://127.0.0.1:8086"
```

#### server_base_url
- **作用**: 服务器的基础访问地址
- **用途**: 用于生成文件下载链接、API回调地址等
- **示例**: 
  - 开发环境: `http://localhost:8086`
  - 生产环境: `https://your-production-domain.com`

#### upload_url_prefix  
- **作用**: 上传文件的URL路径前缀
- **用途**: 与 server_base_url 组合生成完整的文件访问URL
- **默认值**: `/uploads`

#### goview_data_origin_url
- **作用**: GoView组件请求数据的源地址
- **用途**: 在创建GoView项目时设置默认的数据请求地址
- **示例**: 
  - 开发环境: `http://127.0.0.1:8086`
  - 生产环境: `https://your-production-domain.com`

## 使用方法

### 在代码中使用配置

```go
import "ccapi/common"

// 获取全局配置实例
serverConfig := common.GetGlobalServerConfig()

// 生成文件访问URL
fileURL := serverConfig.GetUploadURL("uploads/example.png")
// 结果: http://localhost:8086/uploads/example.png

// 获取存储桶URL（兼容GoView）
bucketURL := serverConfig.GetBucketURL()
// 结果: http://localhost:8086/uploads/

// 直接访问配置项
baseURL := serverConfig.BaseURL
dataOriginURL := serverConfig.GoViewDataOriginURL
```

### 环境配置示例

#### 开发环境 (dev.conf)
```ini
server_base_url = "http://localhost:8086"
upload_url_prefix = "/uploads"
goview_data_origin_url = "http://127.0.0.1:8086"
```

#### 生产环境 (prod.conf)
```ini
server_base_url = "https://api.yourcompany.com"
upload_url_prefix = "/uploads"
goview_data_origin_url = "https://api.yourcompany.com"
```

#### 测试环境 (test.conf)
```ini
server_base_url = "http://test-server:8086"
upload_url_prefix = "/uploads"
goview_data_origin_url = "http://test-server:8086"
```

## 迁移说明

### 已修改的文件

1. **controllers/goview.go**
   - `GetOssInfo()`: bucketURL 现在从配置读取
   - `CreateProject()`: requestOriginUrl 现在从配置读取
   - `UploadFile()`: 文件URL生成现在使用配置
   - `UploadBackgroundImage()`: 文件URL生成现在使用配置

2. **common/config.go** (新增)
   - 提供统一的配置读取和URL生成功能

3. **frontend/vite.config.ts**
   - 代理目标地址现在从环境变量读取

4. **frontend/.env** 和 **frontend/.env.production** (新增)
   - 前端环境变量配置文件

### 配置文件更新

所有环境的配置文件都已添加相应的服务地址配置项。

### 前端配置

前端项目现在也支持通过环境变量配置API地址：

```bash
# .env (开发环境)
VITE_API_BASE_URL=http://localhost:8086

# .env.production (生产环境)
VITE_API_BASE_URL=https://your-production-domain.com
```

## 注意事项

1. **配置优先级**: 环境特定配置文件会覆盖 app.conf 中的默认配置
2. **URL格式**: server_base_url 不应以 `/` 结尾，系统会自动处理
3. **路径前缀**: upload_url_prefix 应以 `/` 开头，系统会自动补充
4. **向后兼容**: 如果配置项不存在，系统会使用默认值确保兼容性

## 扩展

如需添加新的地址配置项：

1. 在配置文件中添加新的配置项
2. 在 `common/config.go` 的 `ServerConfig` 结构体中添加对应字段
3. 在 `GetServerConfig()` 函数中添加读取逻辑
4. 根据需要添加相应的辅助方法

这样可以确保所有地址配置都能统一管理，便于维护和部署。
