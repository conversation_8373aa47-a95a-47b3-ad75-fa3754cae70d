{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "prod": "vite preview --spa", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@visactor/vchart": "^2.0.0", "axios": "^1.10.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "gsap": "^3.12.5", "html2canvas": "^1.4.1", "naive-ui": "^2.40.3", "pinia": "^3.0.3", "screenfull": "^6.0.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/crypto-js": "^4.2.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "daisyui": "^4.12.10", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "~5.8.0", "vite": "^4.5.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}