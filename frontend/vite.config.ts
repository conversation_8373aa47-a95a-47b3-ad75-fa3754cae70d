import { fileURLToPath, URL } from 'node:url'
import { createHash } from 'node:crypto'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// 修复 crypto.hash 问题
if (!globalThis.crypto) {
  globalThis.crypto = {
    hash: createHash
  } as any
}

// https://vite.dev/config/
export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? '/lowcode/' : '/',  // 生产环境使用 /lowcode/，开发环境使用 /
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  define: {
    global: 'globalThis',
    // Vue 编译时特性标志
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
  },
  optimizeDeps: {
    exclude: ['crypto']
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL || 'http://localhost:8086',
        changeOrigin: true,
        secure: false,
      },

    },
  },
  preview: {
    port: 4173,
    open: '/lowcode/',
    // 配置 SPA fallback
    proxy: {
      '^/lowcode/(?!assets/).*': {
        target: 'http://localhost:4173',
        changeOrigin: true,
        rewrite: (path) => '/lowcode/index.html'
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
})
