import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 根据环境确定 baseURL
const getBaseURL = () => {
  // 开发环境使用相对路径（会被 vite proxy 代理）
  if (import.meta.env.DEV) {
    return '/api'
  }

  // 生产环境使用完整的 API 地址
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://pixconsole.pixmoving.city'
  return `${apiBaseUrl}/api`
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: getBaseURL(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 获取token
    const authStore = useAuthStore()
    const token = authStore.token

    // 如果有token，添加到请求头
    if (token) {
      config.headers['X-Token'] = token
    } else if (import.meta.env.DEV) {
      // 开发环境下添加绕过认证的头
      config.headers['X-Dev-Bypass-Auth'] = 'let-me-in'
    }

    console.log('Request:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<any>) => {
    console.log('Response:', response.status, response.config.url, response.data)

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 检查业务状态码 - 后端返回格式: {code: 0/1/401, msg: string, data: any}
    const { data } = response
    if (data.code === 401) {
      // 登录失效 (code: 401 表示需要重新登录)
      console.error('Authentication Error:', data.msg)
      const authStore = useAuthStore()
      authStore.logout()
      router.push('/login')
      throw new Error(data.msg || '登录已失效，请重新登录')
    }

    if (data.code === 1) {
      // 业务错误 (code: 1 表示失败)
      console.error('Business Error:', data.msg)
      throw new Error(data.msg || '请求失败')
    }

    // 成功响应 (code: 0 表示成功)，转换为前端期望的格式
    const transformedResponse = {
      ...response,
      data: {
        success: true,
        data: data.data,
        message: data.msg
      }
    }

    return transformedResponse
  },
  async (error) => {
    console.error('Response Error:', error)
    
    if (error.response) {
      const { status, data } = error.response
      const authStore = useAuthStore()
      
      switch (status) {
        case 401:
          // Token过期或无效，尝试刷新token
          if (authStore.refreshToken && !error.config._retry) {
            error.config._retry = true
            
            try {
              await authStore.refreshAccessToken()
              // 重新发送原请求
              error.config.headers['X-Token'] = authStore.token
              return request(error.config)
            } catch (refreshError) {
              // 刷新失败，跳转到登录页
              authStore.logout()
              router.push('/login')
              throw new Error('登录已过期，请重新登录')
            }
          } else {
            // 没有refreshToken或刷新失败，直接跳转登录页
            authStore.logout()
            router.push('/login')
            throw new Error('登录已过期，请重新登录')
          }
          break
          
        case 403:
          throw new Error('没有权限访问该资源')
          
        case 404:
          throw new Error('请求的资源不存在')
          
        case 500:
          throw new Error('服务器内部错误')
          
        default:
          throw new Error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请稍后重试')
    } else {
      throw new Error('网络错误，请检查网络连接')
    }
  }
)

export default request
