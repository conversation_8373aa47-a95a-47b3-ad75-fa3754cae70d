import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LowCodeConfigView from '../views/LowCodeConfigView.vue'
import LowCodeRuntimeView from '../views/LowCodeRuntimeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/orders',
      name: 'orders',
      component: () => import('../views/OrderView.vue'),
      meta: { requiresAuth: true }
    },

    {
      path: '/config',
      name: 'lowcode',
      component: LowCodeConfigView,
      meta: { requiresAuth: true }
    },
    {
      path: '/runtime',
      name: 'lowcode-runtime',
      component: LowCodeRuntimeView,
      meta: { requiresAuth: true }
    },


  ],
})

// 路由守卫（兼容原有认证系统）
router.beforeEach((to, from, next) => {
  // 检查本地存储中的登录状态（兼容多种token存储方式）
  const token = localStorage.getItem('token') ||
                localStorage.getItem('authToken') ||
                localStorage.getItem('X-Token')
  const refreshToken = localStorage.getItem('refreshToken')
  const expiresIn = localStorage.getItem('expiresIn')

  // 检查认证信息是否完整
  const hasCompleteAuth = !!(token && refreshToken && expiresIn && parseInt(expiresIn) > 0)

  // 如果有token但认证信息不完整，清除所有认证信息
  if (token && !hasCompleteAuth) {
    console.warn('认证信息不完整，清除所有认证数据')
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('expiresIn')
    localStorage.removeItem('user')
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth && !hasCompleteAuth) {
    // 需要认证但未登录或认证信息不完整，跳转到登录页
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // 检查是否需要游客状态（如登录页）
  if (to.meta.requiresGuest && hasCompleteAuth) {
    // 已登录用户访问登录页，跳转到首页
    next({ name: 'home' })
    return
  }

  next()
})

export default router
