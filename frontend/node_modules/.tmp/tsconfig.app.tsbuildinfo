{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/api/auth.ts", "../../src/api/device.ts", "../../src/api/index.ts", "../../src/api/order.ts", "../../src/components/datatable.vue", "../../src/components/pagination.vue", "../../src/components/searchform.vue", "../../src/components/toast.vue", "../../src/components/common/basemodal.vue", "../../src/components/common/index.ts", "../../src/components/lowcode/basicconfigpanel.vue", "../../src/components/lowcode/buttonconfigpanel.vue", "../../src/components/lowcode/configpreview.vue", "../../src/components/lowcode/dynamicparamsconfig.vue", "../../src/components/lowcode/exportconfigpanel.vue", "../../src/components/lowcode/exportmodal.vue", "../../src/components/lowcode/fieldeditmodal.vue", "../../src/components/lowcode/functionhelpermodal.vue", "../../src/components/lowcode/lowcoderenderer.vue", "../../src/components/lowcode/searchconfigpanel.vue", "../../src/components/lowcode/tableconfigpanel.vue", "../../src/composables/usemodal.ts", "../../src/router/index.ts", "../../src/schemas/lowcode-config.schema.ts", "../../src/services/lowcode.ts", "../../src/stores/auth.ts", "../../src/stores/counter.ts", "../../src/types/auth.ts", "../../src/types/common.ts", "../../src/types/index.ts", "../../src/types/lowcode.ts", "../../src/types/modal.ts", "../../src/types/order.ts", "../../src/utils/crypto.ts", "../../src/utils/dynamic-params.ts", "../../src/utils/lowcode.ts", "../../src/utils/modal.ts", "../../src/utils/request.ts", "../../src/utils/toast.ts", "../../src/views/aboutview.vue", "../../src/views/homeview.vue", "../../src/views/loginview.vue", "../../src/views/lowcodeconfigview.vue", "../../src/views/lowcoderuntimeview.vue", "../../src/views/modalexamplesview.vue", "../../src/views/orderview.vue"], "errors": true, "version": "5.8.3"}