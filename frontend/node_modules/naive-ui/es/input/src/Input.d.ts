import type { FormValidationStatus } from '../../form/src/public-types';
import type { OnUpdateValue, Size } from './interface';
import { type InputHTMLAttributes, type PropType, type SlotsType, type TextareaHTMLAttributes, type VNode, type VNodeChild } from 'vue';
import { type ScrollbarInst } from '../../_internal';
import { type ExtractPublicPropTypes, type MaybeArray } from '../../_utils';
export declare const inputProps: {
    bordered: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    type: {
        type: PropType<"text" | "textarea" | "password">;
        default: string;
    };
    placeholder: PropType<string | [string, string]>;
    defaultValue: {
        type: PropType<null | string | [string, string]>;
        default: null;
    };
    value: PropType<null | string | [string, string]>;
    disabled: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    size: PropType<Size>;
    rows: {
        type: PropType<number | string>;
        default: number;
    };
    round: BooleanConstructor;
    minlength: PropType<number | string>;
    maxlength: PropType<number | string>;
    clearable: BooleanConstructor;
    autosize: {
        type: PropType<boolean | {
            minRows?: number;
            maxRows?: number;
        }>;
        default: boolean;
    };
    pair: BooleanConstructor;
    separator: StringConstructor;
    readonly: {
        type: (BooleanConstructor | StringConstructor)[];
        default: boolean;
    };
    passivelyActivated: BooleanConstructor;
    showPasswordOn: PropType<"mousedown" | "click">;
    stateful: {
        type: BooleanConstructor;
        default: boolean;
    };
    autofocus: BooleanConstructor;
    inputProps: PropType<TextareaHTMLAttributes | InputHTMLAttributes>;
    resizable: {
        type: BooleanConstructor;
        default: boolean;
    };
    showCount: BooleanConstructor;
    loading: {
        type: BooleanConstructor;
        default: undefined;
    };
    allowInput: PropType<(value: string) => boolean>;
    renderCount: PropType<(props: {
        value: string;
    }) => VNodeChild>;
    onMousedown: PropType<(e: MouseEvent) => void>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onKeyup: PropType<(e: KeyboardEvent) => void>;
    onInput: PropType<OnUpdateValue>;
    onFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onClick: PropType<MaybeArray<(e: MouseEvent) => void>>;
    onChange: PropType<OnUpdateValue>;
    onClear: PropType<MaybeArray<(e: MouseEvent) => void>>;
    countGraphemes: PropType<(value: string) => number>;
    status: PropType<FormValidationStatus>;
    'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    /** private */
    textDecoration: PropType<string | [string, string]>;
    attrSize: {
        type: NumberConstructor;
        default: number;
    };
    onInputBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onInputFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onDeactivate: PropType<MaybeArray<() => void>>;
    onActivate: PropType<MaybeArray<() => void>>;
    onWrapperFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onWrapperBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    internalDeactivateOnEnter: BooleanConstructor;
    internalForceFocus: BooleanConstructor;
    internalLoadingBeforeSuffix: {
        type: BooleanConstructor;
        default: boolean;
    };
    /** deprecated */
    showPasswordToggle: BooleanConstructor;
    theme: PropType<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>>;
};
export type InputProps = ExtractPublicPropTypes<typeof inputProps>;
export interface InputSlots {
    'clear-icon'?: () => VNode[];
    count?: (props: {
        value: string;
    }) => VNode[];
    'password-invisible-icon'?: () => VNode[];
    'password-visible-icon'?: () => VNode[];
    prefix?: () => VNode[];
    separator?: () => VNode[];
    suffix?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    bordered: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    type: {
        type: PropType<"text" | "textarea" | "password">;
        default: string;
    };
    placeholder: PropType<string | [string, string]>;
    defaultValue: {
        type: PropType<null | string | [string, string]>;
        default: null;
    };
    value: PropType<null | string | [string, string]>;
    disabled: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    size: PropType<Size>;
    rows: {
        type: PropType<number | string>;
        default: number;
    };
    round: BooleanConstructor;
    minlength: PropType<number | string>;
    maxlength: PropType<number | string>;
    clearable: BooleanConstructor;
    autosize: {
        type: PropType<boolean | {
            minRows?: number;
            maxRows?: number;
        }>;
        default: boolean;
    };
    pair: BooleanConstructor;
    separator: StringConstructor;
    readonly: {
        type: (BooleanConstructor | StringConstructor)[];
        default: boolean;
    };
    passivelyActivated: BooleanConstructor;
    showPasswordOn: PropType<"mousedown" | "click">;
    stateful: {
        type: BooleanConstructor;
        default: boolean;
    };
    autofocus: BooleanConstructor;
    inputProps: PropType<TextareaHTMLAttributes | InputHTMLAttributes>;
    resizable: {
        type: BooleanConstructor;
        default: boolean;
    };
    showCount: BooleanConstructor;
    loading: {
        type: BooleanConstructor;
        default: undefined;
    };
    allowInput: PropType<(value: string) => boolean>;
    renderCount: PropType<(props: {
        value: string;
    }) => VNodeChild>;
    onMousedown: PropType<(e: MouseEvent) => void>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onKeyup: PropType<(e: KeyboardEvent) => void>;
    onInput: PropType<OnUpdateValue>;
    onFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onClick: PropType<MaybeArray<(e: MouseEvent) => void>>;
    onChange: PropType<OnUpdateValue>;
    onClear: PropType<MaybeArray<(e: MouseEvent) => void>>;
    countGraphemes: PropType<(value: string) => number>;
    status: PropType<FormValidationStatus>;
    'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    /** private */
    textDecoration: PropType<string | [string, string]>;
    attrSize: {
        type: NumberConstructor;
        default: number;
    };
    onInputBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onInputFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onDeactivate: PropType<MaybeArray<() => void>>;
    onActivate: PropType<MaybeArray<() => void>>;
    onWrapperFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onWrapperBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    internalDeactivateOnEnter: BooleanConstructor;
    internalForceFocus: BooleanConstructor;
    internalLoadingBeforeSuffix: {
        type: BooleanConstructor;
        default: boolean;
    };
    /** deprecated */
    showPasswordToggle: BooleanConstructor;
    theme: PropType<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>>;
}>, {
    wrapperElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    inputElRef: import("vue").Ref<HTMLInputElement | null, HTMLInputElement | null>;
    inputMirrorElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    inputEl2Ref: import("vue").Ref<HTMLInputElement | null, HTMLInputElement | null>;
    textareaElRef: import("vue").Ref<HTMLTextAreaElement | null, HTMLTextAreaElement | null>;
    textareaMirrorElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    textareaScrollbarInstRef: import("vue").Ref<{
        $el: HTMLElement;
        containerRef: HTMLElement | null;
        contentRef: HTMLElement | null;
        containerScrollTop: number;
        syncUnifiedContainer: () => void;
        scrollTo: import("../../_internal/scrollbar/src/Scrollbar").ScrollTo;
        scrollBy: import("../../_internal/scrollbar/src/Scrollbar").ScrollBy;
        sync: () => void;
        handleMouseEnterWrapper: () => void;
        handleMouseLeaveWrapper: () => void;
    } | null, ScrollbarInst | {
        $el: HTMLElement;
        containerRef: HTMLElement | null;
        contentRef: HTMLElement | null;
        containerScrollTop: number;
        syncUnifiedContainer: () => void;
        scrollTo: import("../../_internal/scrollbar/src/Scrollbar").ScrollTo;
        scrollBy: import("../../_internal/scrollbar/src/Scrollbar").ScrollBy;
        sync: () => void;
        handleMouseEnterWrapper: () => void;
        handleMouseLeaveWrapper: () => void;
    } | null>;
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    uncontrolledValue: import("vue").Ref<string | [string, string] | null, string | [string, string] | null>;
    mergedValue: import("vue").ComputedRef<string | [string, string] | null>;
    passwordVisible: import("vue").Ref<boolean, boolean>;
    mergedPlaceholder: import("vue").ComputedRef<[string] | [string, string]>;
    showPlaceholder1: import("vue").ComputedRef<string | false>;
    showPlaceholder2: import("vue").ComputedRef<boolean | "" | undefined>;
    mergedFocus: import("vue").ComputedRef<boolean>;
    isComposing: import("vue").Ref<boolean, boolean>;
    activated: import("vue").Ref<boolean, boolean>;
    showClearButton: import("vue").ComputedRef<boolean>;
    mergedSize: import("vue").ComputedRef<"small" | "medium" | "large" | "tiny">;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    textDecorationStyle: import("vue").ComputedRef<string[] | {
        textDecoration: string;
    }[]>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    mergedBordered: import("vue").ComputedRef<boolean>;
    mergedShowPasswordOn: import("vue").ComputedRef<"click" | "mousedown" | undefined>;
    placeholderStyle: import("vue").Ref<{
        top: string;
    }, {
        top: string;
    } | {
        top: string;
    }>;
    mergedStatus: import("vue").ComputedRef<FormValidationStatus | undefined>;
    textAreaScrollContainerWidth: import("vue").Ref<number | undefined, number | undefined>;
    handleTextAreaScroll: (e: Event) => void;
    handleCompositionStart: () => void;
    handleCompositionEnd: (e: CompositionEvent) => void;
    handleInput: (e: InputEvent | CompositionEvent | Event, index?: 0 | 1, event?: string) => void;
    handleInputBlur: (e: FocusEvent) => void;
    handleInputFocus: (e: FocusEvent, index: number) => void;
    handleWrapperBlur: (e: FocusEvent) => void;
    handleWrapperFocus: (e: FocusEvent) => void;
    handleMouseEnter: () => void;
    handleMouseLeave: () => void;
    handleMouseDown: (e: MouseEvent) => void;
    handleChange: (e: Event, index?: 0 | 1) => void;
    handleClick: (e: MouseEvent) => void;
    handleClear: (e: MouseEvent) => void;
    handlePasswordToggleClick: () => void;
    handlePasswordToggleMousedown: (e: MouseEvent) => void;
    handleWrapperKeydown: (e: KeyboardEvent) => void;
    handleWrapperKeyup: (e: KeyboardEvent) => void;
    handleTextAreaMirrorResize: () => void;
    getTextareaScrollContainer: () => HTMLTextAreaElement | null;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        };
        peers: any;
        peerOverrides: {
            [x: string]: any;
        };
    }>;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-count-text-color': string;
        '--n-count-text-color-disabled': string;
        '--n-color': string;
        '--n-font-size': string;
        '--n-font-weight': string;
        '--n-border-radius': string;
        '--n-height': string;
        '--n-padding-left': string;
        '--n-padding-right': string;
        '--n-text-color': string;
        '--n-caret-color': string;
        '--n-text-decoration-color': string;
        '--n-border': string;
        '--n-border-disabled': string;
        '--n-border-hover': string;
        '--n-border-focus': string;
        '--n-placeholder-color': string;
        '--n-placeholder-color-disabled': string;
        '--n-icon-size': string;
        '--n-line-height-textarea': string;
        '--n-color-disabled': string;
        '--n-color-focus': string;
        '--n-text-color-disabled': string;
        '--n-box-shadow-focus': string;
        '--n-loading-color': string;
        '--n-caret-color-warning': string;
        '--n-color-focus-warning': string;
        '--n-box-shadow-focus-warning': string;
        '--n-border-warning': string;
        '--n-border-focus-warning': string;
        '--n-border-hover-warning': string;
        '--n-loading-color-warning': string;
        '--n-caret-color-error': string;
        '--n-color-focus-error': string;
        '--n-box-shadow-focus-error': string;
        '--n-border-error': string;
        '--n-border-focus-error': string;
        '--n-border-hover-error': string;
        '--n-loading-color-error': string;
        '--n-clear-color': string;
        '--n-clear-size': string;
        '--n-clear-color-hover': string;
        '--n-clear-color-pressed': string;
        '--n-icon-color': string;
        '--n-icon-color-hover': string;
        '--n-icon-color-pressed': string;
        '--n-icon-color-disabled': string;
        '--n-suffix-text-color': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
    isCompositing: import("vue").Ref<boolean>;
    blur: () => void;
    clear: () => void;
    focus: () => void;
    select: () => void;
    activate: () => void;
    deactivate: () => void;
    scrollTo: (options: ScrollToOptions) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    bordered: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    type: {
        type: PropType<"text" | "textarea" | "password">;
        default: string;
    };
    placeholder: PropType<string | [string, string]>;
    defaultValue: {
        type: PropType<null | string | [string, string]>;
        default: null;
    };
    value: PropType<null | string | [string, string]>;
    disabled: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    size: PropType<Size>;
    rows: {
        type: PropType<number | string>;
        default: number;
    };
    round: BooleanConstructor;
    minlength: PropType<number | string>;
    maxlength: PropType<number | string>;
    clearable: BooleanConstructor;
    autosize: {
        type: PropType<boolean | {
            minRows?: number;
            maxRows?: number;
        }>;
        default: boolean;
    };
    pair: BooleanConstructor;
    separator: StringConstructor;
    readonly: {
        type: (BooleanConstructor | StringConstructor)[];
        default: boolean;
    };
    passivelyActivated: BooleanConstructor;
    showPasswordOn: PropType<"mousedown" | "click">;
    stateful: {
        type: BooleanConstructor;
        default: boolean;
    };
    autofocus: BooleanConstructor;
    inputProps: PropType<TextareaHTMLAttributes | InputHTMLAttributes>;
    resizable: {
        type: BooleanConstructor;
        default: boolean;
    };
    showCount: BooleanConstructor;
    loading: {
        type: BooleanConstructor;
        default: undefined;
    };
    allowInput: PropType<(value: string) => boolean>;
    renderCount: PropType<(props: {
        value: string;
    }) => VNodeChild>;
    onMousedown: PropType<(e: MouseEvent) => void>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onKeyup: PropType<(e: KeyboardEvent) => void>;
    onInput: PropType<OnUpdateValue>;
    onFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onClick: PropType<MaybeArray<(e: MouseEvent) => void>>;
    onChange: PropType<OnUpdateValue>;
    onClear: PropType<MaybeArray<(e: MouseEvent) => void>>;
    countGraphemes: PropType<(value: string) => number>;
    status: PropType<FormValidationStatus>;
    'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    /** private */
    textDecoration: PropType<string | [string, string]>;
    attrSize: {
        type: NumberConstructor;
        default: number;
    };
    onInputBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onInputFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onDeactivate: PropType<MaybeArray<() => void>>;
    onActivate: PropType<MaybeArray<() => void>>;
    onWrapperFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    onWrapperBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    internalDeactivateOnEnter: BooleanConstructor;
    internalForceFocus: BooleanConstructor;
    internalLoadingBeforeSuffix: {
        type: BooleanConstructor;
        default: boolean;
    };
    /** deprecated */
    showPasswordToggle: BooleanConstructor;
    theme: PropType<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
        fontWeight: string;
        countTextColorDisabled: string;
        countTextColor: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        lineHeight: string;
        lineHeightTextarea: string;
        borderRadius: string;
        iconSize: string;
        groupLabelColor: string;
        groupLabelTextColor: string;
        textColor: string;
        textColorDisabled: string;
        textDecorationColor: string;
        caretColor: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorFocus: string;
        groupLabelBorder: string;
        border: string;
        borderHover: string;
        borderDisabled: string;
        borderFocus: string;
        boxShadowFocus: string;
        loadingColor: string;
        loadingColorWarning: string;
        borderWarning: string;
        borderHoverWarning: string;
        colorFocusWarning: string;
        borderFocusWarning: string;
        boxShadowFocusWarning: string;
        caretColorWarning: string;
        loadingColorError: string;
        borderError: string;
        borderHoverError: string;
        colorFocusError: string;
        borderFocusError: string;
        boxShadowFocusError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        iconColor: string;
        iconColorDisabled: string;
        iconColorHover: string;
        iconColorPressed: string;
        suffixTextColor: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        clearSize: string;
    }, any>>>;
}>> & Readonly<{}>, {
    type: "textarea" | "text" | "password";
    readonly: string | boolean;
    disabled: boolean | undefined;
    round: boolean;
    autofocus: boolean;
    loading: boolean;
    autosize: boolean | {
        minRows?: number;
        maxRows?: number;
    };
    bordered: boolean | undefined;
    clearable: boolean;
    defaultValue: string | [string, string] | null;
    resizable: boolean;
    pair: boolean;
    rows: string | number;
    passivelyActivated: boolean;
    stateful: boolean;
    showCount: boolean;
    attrSize: number;
    internalDeactivateOnEnter: boolean;
    internalForceFocus: boolean;
    internalLoadingBeforeSuffix: boolean;
    showPasswordToggle: boolean;
}, SlotsType<InputSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
