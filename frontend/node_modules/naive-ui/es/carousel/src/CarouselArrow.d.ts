declare const _default: import("vue").DefineComponent<{}, {
    mergedClsPrefix: import("vue").Ref<string, string>;
    isVertical: () => boolean;
    isPrevDisabled: () => boolean;
    isNextDisabled: () => boolean;
    prev: () => void;
    next: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
