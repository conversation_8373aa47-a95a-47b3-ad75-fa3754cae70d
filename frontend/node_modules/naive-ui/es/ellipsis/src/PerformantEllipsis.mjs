import { defineComponent, h, mergeProps, ref } from 'vue';
import { useStyle } from "../../_mixins/index.mjs";
import { useMergedClsPrefix } from "../../_mixins/use-config.mjs";
import Ellipsis, { createCursorClass, createLineClampClass, ellipsisProps } from "./Ellipsis.mjs";
import style from "./styles/index.cssr.mjs";
export const NPerformantEllipsis = defineComponent({
  name: 'PerformantEllipsis',
  props: ellipsisProps,
  inheritAttrs: false,
  setup(props, {
    attrs,
    slots
  }) {
    const mouseEnteredRef = ref(false);
    const mergedClsPrefixRef = useMergedClsPrefix();
    useStyle('-ellipsis', style, mergedClsPrefixRef);
    // Modified from Ellipsis.tsx
    const renderTrigger = () => {
      const {
        lineClamp
      } = props;
      const mergedClsPrefix = mergedClsPrefixRef.value;
      return h("span", Object.assign({}, mergeProps(attrs, {
        class: [`${mergedClsPrefix}-ellipsis`, lineClamp !== undefined ? createLineClampClass(mergedClsPrefix) : undefined, props.expandTrigger === 'click' ? createCursorClass(mergedClsPrefix, 'pointer') : undefined],
        style: lineClamp === undefined ? {
          textOverflow: 'ellipsis'
        } : {
          '-webkit-line-clamp': lineClamp
        }
      }), {
        onMouseenter: () => {
          mouseEnteredRef.value = true;
        }
      }), lineClamp ? slots : h("span", null, slots));
    };
    return {
      mouseEntered: mouseEnteredRef,
      renderTrigger
    };
  },
  render() {
    if (this.mouseEntered) {
      return h(Ellipsis, mergeProps({}, this.$attrs, this.$props), this.$slots);
    } else {
      return this.renderTrigger();
    }
  }
});