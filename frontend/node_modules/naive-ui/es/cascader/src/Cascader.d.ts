import type { InternalSelectionInst } from '../../_internal';
import type { ExtractPublicPropTypes, MaybeArray } from '../../_utils';
import type { FormValidationStatus } from '../../form/src/public-types';
import type { PopoverProps } from '../../popover';
import type { SelectBaseOption } from '../../select/src/interface';
import type { CascaderMenuInstance, CascaderOption, ExpandTrigger, Filter, Key, OnLoad, OnUpdateValue, SelectMenuInstance, Value } from './interface';
import { type CheckStrategy } from 'treemate';
import { type CSSProperties, type HTMLAttributes, type PropType, type SlotsType, type VNode, type VNodeChild } from 'vue';
import { type FollowerInst, type FollowerPlacement } from 'vueuc';
export declare const cascaderProps: {
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly bordered: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly options: {
        readonly type: PropType<CascaderOption[]>;
        readonly default: () => never[];
    };
    readonly value: PropType<Value | null>;
    readonly defaultValue: {
        readonly type: PropType<Value | null>;
        readonly default: null;
    };
    readonly placeholder: StringConstructor;
    readonly multiple: BooleanConstructor;
    readonly size: PropType<"small" | "medium" | "large">;
    readonly filterable: BooleanConstructor;
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly expandTrigger: {
        readonly type: PropType<ExpandTrigger>;
        readonly default: "click";
    };
    readonly clearable: BooleanConstructor;
    readonly clearFilterAfterSelect: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly remote: BooleanConstructor;
    readonly onLoad: PropType<OnLoad>;
    readonly separator: {
        readonly type: StringConstructor;
        readonly default: " / ";
    };
    readonly filter: PropType<Filter>;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly cascade: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly leafOnly: BooleanConstructor;
    readonly showPath: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly show: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly maxTagCount: PropType<number | "responsive">;
    readonly ellipsisTagPopoverProps: PropType<PopoverProps>;
    readonly menuProps: PropType<HTMLAttributes>;
    readonly filterMenuProps: PropType<HTMLAttributes>;
    readonly virtualScroll: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly checkStrategy: {
        readonly type: PropType<CheckStrategy>;
        readonly default: "all";
    };
    readonly valueField: {
        readonly type: StringConstructor;
        readonly default: "value";
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly renderLabel: PropType<(option: CascaderOption, checked: boolean) => VNodeChild>;
    readonly status: PropType<FormValidationStatus>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:show': PropType<MaybeArray<(show: boolean) => void>>;
    readonly onUpdateShow: PropType<MaybeArray<(show: boolean) => void>>;
    readonly onBlur: PropType<(e: FocusEvent) => void>;
    readonly onFocus: PropType<(e: FocusEvent) => void>;
    readonly getColumnStyle: PropType<(detail: {
        level: number;
    }) => string | CSSProperties>;
    readonly renderPrefix: PropType<(props: {
        option: CascaderOption;
        checked: boolean;
        node: VNode | null;
    }) => VNodeChild>;
    readonly renderSuffix: PropType<(props: {
        option: CascaderOption;
        checked: boolean;
        node: VNode | null;
    }) => VNodeChild>;
    readonly onChange: PropType<MaybeArray<OnUpdateValue> | undefined>;
    readonly theme: PropType<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
};
export type CascaderProps = ExtractPublicPropTypes<typeof cascaderProps>;
export interface CascaderSlots {
    action?: () => VNode[];
    arrow?: () => VNode[];
    empty?: () => VNode[];
    'not-found'?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly bordered: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly options: {
        readonly type: PropType<CascaderOption[]>;
        readonly default: () => never[];
    };
    readonly value: PropType<Value | null>;
    readonly defaultValue: {
        readonly type: PropType<Value | null>;
        readonly default: null;
    };
    readonly placeholder: StringConstructor;
    readonly multiple: BooleanConstructor;
    readonly size: PropType<"small" | "medium" | "large">;
    readonly filterable: BooleanConstructor;
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly expandTrigger: {
        readonly type: PropType<ExpandTrigger>;
        readonly default: "click";
    };
    readonly clearable: BooleanConstructor;
    readonly clearFilterAfterSelect: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly remote: BooleanConstructor;
    readonly onLoad: PropType<OnLoad>;
    readonly separator: {
        readonly type: StringConstructor;
        readonly default: " / ";
    };
    readonly filter: PropType<Filter>;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly cascade: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly leafOnly: BooleanConstructor;
    readonly showPath: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly show: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly maxTagCount: PropType<number | "responsive">;
    readonly ellipsisTagPopoverProps: PropType<PopoverProps>;
    readonly menuProps: PropType<HTMLAttributes>;
    readonly filterMenuProps: PropType<HTMLAttributes>;
    readonly virtualScroll: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly checkStrategy: {
        readonly type: PropType<CheckStrategy>;
        readonly default: "all";
    };
    readonly valueField: {
        readonly type: StringConstructor;
        readonly default: "value";
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly renderLabel: PropType<(option: CascaderOption, checked: boolean) => VNodeChild>;
    readonly status: PropType<FormValidationStatus>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:show': PropType<MaybeArray<(show: boolean) => void>>;
    readonly onUpdateShow: PropType<MaybeArray<(show: boolean) => void>>;
    readonly onBlur: PropType<(e: FocusEvent) => void>;
    readonly onFocus: PropType<(e: FocusEvent) => void>;
    readonly getColumnStyle: PropType<(detail: {
        level: number;
    }) => string | CSSProperties>;
    readonly renderPrefix: PropType<(props: {
        option: CascaderOption;
        checked: boolean;
        node: VNode | null;
    }) => VNodeChild>;
    readonly renderSuffix: PropType<(props: {
        option: CascaderOption;
        checked: boolean;
        node: VNode | null;
    }) => VNodeChild>;
    readonly onChange: PropType<MaybeArray<OnUpdateValue> | undefined>;
    readonly theme: PropType<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
}>, {
    handleTriggerResize: () => void;
    mergedStatus: import("vue").ComputedRef<FormValidationStatus | undefined>;
    selectMenuFollowerRef: import("vue").Ref<{
        syncPosition: () => void;
    } | null, FollowerInst | {
        syncPosition: () => void;
    } | null>;
    cascaderMenuFollowerRef: import("vue").Ref<{
        syncPosition: () => void;
    } | null, FollowerInst | {
        syncPosition: () => void;
    } | null>;
    triggerInstRef: import("vue").Ref<{
        isComposing: boolean;
        focus: () => void;
        focusInput: () => void;
        blur: () => void;
        blurInput: () => void;
        $el: HTMLElement;
    } | null, InternalSelectionInst | {
        isComposing: boolean;
        focus: () => void;
        focusInput: () => void;
        blur: () => void;
        blurInput: () => void;
        $el: HTMLElement;
    } | null>;
    selectMenuInstRef: import("vue").Ref<{
        prev: () => void;
        next: () => void;
        enter: () => boolean;
    } | null, SelectMenuInstance | {
        prev: () => void;
        next: () => void;
        enter: () => boolean;
    } | null>;
    cascaderMenuInstRef: import("vue").Ref<{
        $el: HTMLElement;
        scroll: (depth: number, index: number, elSize: number) => void;
        showErrorMessage: (label: string) => void;
    } | null, CascaderMenuInstance | {
        $el: HTMLElement;
        scroll: (depth: number, index: number, elSize: number) => void;
        showErrorMessage: (label: string) => void;
    } | null>;
    mergedBordered: import("vue").ComputedRef<boolean>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    namespace: import("vue").ComputedRef<string | undefined>;
    mergedValue: import("vue").ComputedRef<string | number | import("./interface").ValueAtom[] | import("./interface").ValueAtom[] | null>;
    mergedShow: import("vue").ComputedRef<boolean>;
    showSelectMenu: import("vue").ComputedRef<boolean>;
    pattern: import("vue").Ref<string, string>;
    treeMate: import("vue").ComputedRef<import("treemate").TreeMate<CascaderOption, CascaderOption, CascaderOption>>;
    mergedSize: import("vue").ComputedRef<"small" | "medium" | "large">;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    localizedPlaceholder: import("vue").ComputedRef<string>;
    selectedOption: import("vue").ComputedRef<{
        label: any;
        value: import("treemate").Key;
    } | null>;
    selectedOptions: import("vue").ComputedRef<{
        label: any;
        value: import("treemate").Key;
    }[]>;
    adjustedTo: import("vue").ComputedRef<string | HTMLElement>;
    menuModel: import("vue").ComputedRef<import("treemate").TreeNode<CascaderOption, CascaderOption, CascaderOption>[][]>;
    handleMenuTabout: () => void;
    handleMenuFocus: (e: FocusEvent) => void;
    handleMenuBlur: (e: FocusEvent) => void;
    handleMenuKeydown: (e: KeyboardEvent) => void;
    handleMenuMousedown: (e: MouseEvent) => void;
    handleTriggerFocus: (e: FocusEvent) => void;
    handleTriggerBlur: (e: FocusEvent) => void;
    handleTriggerClick: () => void;
    handleClear: (e: MouseEvent) => void;
    handleDeleteOption: (option: SelectBaseOption) => void;
    handlePatternInput: (e: InputEvent) => void;
    handleKeydown: (e: KeyboardEvent) => void;
    focused: import("vue").Ref<boolean, boolean>;
    optionHeight: import("vue").ComputedRef<string>;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            menuBorderRadius: string;
            menuColor: string;
            menuBoxShadow: string;
            menuDividerColor: string;
            menuHeight: string;
            optionArrowColor: string;
            optionHeight: string;
            optionFontSize: string;
            optionColorHover: string;
            optionTextColor: string;
            optionTextColorActive: string;
            optionTextColorDisabled: string;
            optionCheckMarkColor: string;
            loadingColor: string;
            columnWidth: string;
        };
        peers: {
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        };
        peerOverrides: {
            InternalSelectMenu?: {
                peers?: {
                    Scrollbar?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>> | undefined;
                    Empty?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
            InternalSelection?: {
                peers?: {
                    Popover?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
            Scrollbar?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Checkbox?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Empty?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }>;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-menu-border-radius': string;
        '--n-menu-box-shadow': string;
        '--n-menu-height': string;
        '--n-column-width': string;
        '--n-menu-color': string;
        '--n-menu-divider-color': string;
        '--n-option-height': string;
        '--n-option-font-size': string;
        '--n-option-text-color': string;
        '--n-option-text-color-disabled': string;
        '--n-option-text-color-active': string;
        '--n-option-color-hover': string;
        '--n-option-check-mark-color': string;
        '--n-option-arrow-color': string;
        '--n-menu-mask-color': string;
        '--n-loading-color': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
    focus: () => void;
    blur: () => void;
    getCheckedData: () => {
        keys: Key[];
        options: Array<CascaderOption | null>;
    };
    getIndeterminateData: () => {
        keys: Key[];
        options: Array<CascaderOption | null>;
    };
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly bordered: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly options: {
        readonly type: PropType<CascaderOption[]>;
        readonly default: () => never[];
    };
    readonly value: PropType<Value | null>;
    readonly defaultValue: {
        readonly type: PropType<Value | null>;
        readonly default: null;
    };
    readonly placeholder: StringConstructor;
    readonly multiple: BooleanConstructor;
    readonly size: PropType<"small" | "medium" | "large">;
    readonly filterable: BooleanConstructor;
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly expandTrigger: {
        readonly type: PropType<ExpandTrigger>;
        readonly default: "click";
    };
    readonly clearable: BooleanConstructor;
    readonly clearFilterAfterSelect: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly remote: BooleanConstructor;
    readonly onLoad: PropType<OnLoad>;
    readonly separator: {
        readonly type: StringConstructor;
        readonly default: " / ";
    };
    readonly filter: PropType<Filter>;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly cascade: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly leafOnly: BooleanConstructor;
    readonly showPath: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly show: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly maxTagCount: PropType<number | "responsive">;
    readonly ellipsisTagPopoverProps: PropType<PopoverProps>;
    readonly menuProps: PropType<HTMLAttributes>;
    readonly filterMenuProps: PropType<HTMLAttributes>;
    readonly virtualScroll: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly checkStrategy: {
        readonly type: PropType<CheckStrategy>;
        readonly default: "all";
    };
    readonly valueField: {
        readonly type: StringConstructor;
        readonly default: "value";
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly renderLabel: PropType<(option: CascaderOption, checked: boolean) => VNodeChild>;
    readonly status: PropType<FormValidationStatus>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:show': PropType<MaybeArray<(show: boolean) => void>>;
    readonly onUpdateShow: PropType<MaybeArray<(show: boolean) => void>>;
    readonly onBlur: PropType<(e: FocusEvent) => void>;
    readonly onFocus: PropType<(e: FocusEvent) => void>;
    readonly getColumnStyle: PropType<(detail: {
        level: number;
    }) => string | CSSProperties>;
    readonly renderPrefix: PropType<(props: {
        option: CascaderOption;
        checked: boolean;
        node: VNode | null;
    }) => VNodeChild>;
    readonly renderSuffix: PropType<(props: {
        option: CascaderOption;
        checked: boolean;
        node: VNode | null;
    }) => VNodeChild>;
    readonly onChange: PropType<MaybeArray<OnUpdateValue> | undefined>;
    readonly theme: PropType<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Cascader", {
        menuBorderRadius: string;
        menuColor: string;
        menuBoxShadow: string;
        menuDividerColor: string;
        menuHeight: string;
        optionArrowColor: string;
        optionHeight: string;
        optionFontSize: string;
        optionColorHover: string;
        optionTextColor: string;
        optionTextColorActive: string;
        optionTextColorDisabled: string;
        optionCheckMarkColor: string;
        loadingColor: string;
        columnWidth: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
}>> & Readonly<{}>, {
    readonly options: CascaderOption[];
    readonly to: string | boolean | HTMLElement;
    readonly disabled: boolean | undefined;
    readonly show: boolean | undefined;
    readonly separator: string;
    readonly multiple: boolean;
    readonly bordered: boolean | undefined;
    readonly placement: FollowerPlacement;
    readonly labelField: string;
    readonly virtualScroll: boolean;
    readonly valueField: string;
    readonly filterable: boolean;
    readonly clearable: boolean;
    readonly expandTrigger: ExpandTrigger;
    readonly clearFilterAfterSelect: boolean;
    readonly defaultValue: Value | null;
    readonly remote: boolean;
    readonly childrenField: string;
    readonly allowCheckingNotLoaded: boolean;
    readonly cascade: boolean;
    readonly checkStrategy: CheckStrategy;
    readonly disabledField: string;
    readonly leafOnly: boolean;
    readonly showPath: boolean;
}, SlotsType<CascaderSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
