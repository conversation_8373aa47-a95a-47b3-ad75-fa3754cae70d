// Generated by ChatGPT
export function splitAndMarkByRegex(str, regex) {
  const result = [];
  let lastIndex = 0;
  let match;
  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(str)) !== null) {
    if (match.index > lastIndex) {
      result.push({
        text: str.slice(lastIndex, match.index),
        isMatch: false
      });
    }
    result.push({
      text: match[0],
      isMatch: true
    });
    lastIndex = regex.lastIndex;
    if (!regex.global) {
      break;
    }
  }
  if (lastIndex < str.length) {
    result.push({
      text: str.slice(lastIndex),
      isMatch: false
    });
  }
  return result;
}