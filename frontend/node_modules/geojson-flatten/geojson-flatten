#!/usr/bin/env node

var flatten = require("./"),
  fs = require("fs"),
  getStdin = require("get-stdin"),
  argv = require("minimist")(process.argv.slice(2));

if (process.stdin.isTTY && !argv._[0]) {
  process.stdout.write(`
Usage:
	cat input.geojson | geojson-flatten > flattened.geojson
\n`);
  process.exit(1);
}

if (argv._.length) {
  convert(fs.readFileSync(argv._[0], "utf8"));
} else {
  getStdin().then(convert);
}

function convert(data) {
  process.stdout.write(JSON.stringify(flatten(JSON.parse(data)), argv));
}
