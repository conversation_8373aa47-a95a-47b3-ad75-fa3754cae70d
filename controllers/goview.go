package controllers

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	"ccapi/models/pix"
	"ccapi/common"
)

// InitGoViewRouter 初始化GoView路由
func InitGoViewRouter() beego.LinkNamespace {
	return beego.NSNamespace("/goview",
		// 系统模块
		beego.NSNamespace("/sys",
			beego.NSRouter("/getOssInfo", &GoViewController{}, "get:GetOssInfo"),
		),
		// 项目模块
		beego.NSNamespace("/project",
			beego.NSRouter("/list", &GoViewController{}, "get:ProjectList"),
			beego.NSRouter("/create", &GoViewController{}, "post:CreateProject"),
			beego.NSRouter("/getData", &GoViewController{}, "get:GetProjectData"),
			beego.NSRouter("/save/data", &GoViewController{}, "post:SaveProjectData"),
			beego.NSRouter("/edit", &GoViewController{}, "post:EditProject"),
			beego.NSRouter("/delete", &GoViewController{}, "delete:DeleteProject"),
			beego.NSRouter("/publish", &GoViewController{}, "put:PublishProject"),
			beego.NSRouter("/upload", &GoViewController{}, "post:UploadFile"),
			beego.NSRouter("/uploadBackground", &GoViewController{}, "post:UploadBackgroundImage"),
		),
		// 静态文件服务
		beego.NSRouter("/uploads/*", &GoViewController{}, "get:ServeUploadFile"),
	)
}

// GoViewController GoView相关接口控制器
type GoViewController struct {
	BaseController
}

// GoViewProjectResponse 项目响应数据结构
type GoViewProjectResponse struct {
	ID           string      `json:"id"`
	ProjectName  string      `json:"projectName"`
	State        int         `json:"state"`        // -1: 未发布, 1: 已发布
	CreateTime   string      `json:"createTime"`
	IndexImage   string      `json:"indexImage"`
	CreateUserId string      `json:"createUserId"`
	Remarks      string      `json:"remarks"`
	Content      interface{} `json:"content,omitempty"` // 项目配置内容，可以是字符串或对象
}

// 登录相关结构体已废弃，统一使用 CCAPI 认证系统

// 项目ID计数器（用于生成新的项目ID）
var projectCounter = 1

// Prepare 预处理，设置CORS
func (c *GoViewController) Prepare() {
	// 设置CORS
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, X-Token, Authorization")

	// 处理OPTIONS请求
	if c.Ctx.Request.Method == "OPTIONS" {
		c.Ctx.Output.SetStatus(200)
		return
	}

	// GoView的认证由中间件处理，这里不需要额外检查
	// 因为我们已经在middleware.go中将GoView的接口添加到了noCheckUri列表
}

// 登录和登出功能已废弃，统一使用 CCAPI 认证系统

// GetOssInfo 获取OSS配置信息
func (c *GoViewController) GetOssInfo() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	// 获取服务器配置
	serverConfig := common.GetGlobalServerConfig()

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"data": map[string]interface{}{
			"bucketURL": serverConfig.GetBucketURL(), // 从配置文件读取文件上传地址
		},
		"msg": "获取成功",
	}
	c.ServeJSON()
}

// ProjectList 获取项目列表
func (c *GoViewController) ProjectList() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	o := orm.NewOrm()
	var projects []*pix.GoViewProject

	_, err = o.QueryTable("goview_projects").All(&projects)
	if err != nil {
		logs.Error("查询项目列表失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "查询项目列表失败",
		}
		c.ServeJSON()
		return
	}

	// 转换为响应格式，不返回content字段（项目列表不需要详细内容）
	responseProjects := make([]*GoViewProjectResponse, 0)
	for _, project := range projects {
		responseProject := &GoViewProjectResponse{
			ID:           project.ID,
			ProjectName:  project.ProjectName,
			State:        project.State,
			CreateTime:   project.CreateTime.Format("2006-01-02 15:04:05"),
			IndexImage:   project.IndexImage,
			CreateUserId: project.CreateUserId,
			Remarks:      project.Remarks,
			Content:      "", // 列表不返回详细内容
		}
		responseProjects = append(responseProjects, responseProject)
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"data": responseProjects,
		"msg":  "获取成功",
	}
	c.ServeJSON()
}

// CreateProject 创建新项目
func (c *GoViewController) CreateProject() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	var projectData map[string]interface{}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &projectData); err != nil {
		logs.Error("解析项目数据失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	projectName, _ := projectData["projectName"].(string)
	if projectName == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目名称不能为空",
		}
		c.ServeJSON()
		return
	}

	// 创建新项目
	projectID := strconv.Itoa(projectCounter)
	projectCounter++

	// 获取服务器配置
	serverConfig := common.GetGlobalServerConfig()

	// 创建默认的画布内容 - 符合GoView的ChartEditStorage结构
	defaultContent := map[string]interface{}{
		"editCanvasConfig": map[string]interface{}{
			"width":  1920,
			"height": 1080,
		},
		"requestGlobalConfig": map[string]interface{}{
			"requestDataPond": []interface{}{},
			"requestOriginUrl": serverConfig.GoViewDataOriginURL, // 从配置文件读取
		},
		"componentList": []interface{}{},
	}

	// 将默认内容转换为JSON字符串
	contentBytes, err := json.Marshal(defaultContent)
	if err != nil {
		logs.Error("序列化默认内容失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "创建项目失败",
		}
		c.ServeJSON()
		return
	}

	project := &pix.GoViewProject{
		ID:           projectID,
		ProjectName:  projectName,
		State:        -1, // 未发布
		IndexImage:   "",
		CreateUserId: "1",
		Remarks:      "",
		Content:      string(contentBytes),
	}

	o := orm.NewOrm()
	_, err = o.Insert(project)
	if err != nil {
		logs.Error("插入项目失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "创建项目失败",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"data": map[string]interface{}{
			"id": projectID,
		},
		"msg": "创建成功",
	}
	c.ServeJSON()
}

// GetProjectData 获取项目详情
func (c *GoViewController) GetProjectData() {
	projectID := c.GetString("projectId")
	if projectID == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目ID不能为空",
		}
		c.ServeJSON()
		return
	}

	o := orm.NewOrm()
	project := &pix.GoViewProject{ID: projectID}
	err := o.Read(project)
	if err != nil {
		if err == orm.ErrNoRows {
			c.Data["json"] = map[string]interface{}{
				"code": 404,
				"msg":  "项目不存在",
			}
		} else {
			logs.Error("查询项目失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "查询项目失败",
			}
		}
		c.ServeJSON()
		return
	}

	// 解析content JSON字符串为对象
	var contentObj interface{}
	if project.Content != "" {
		if err := json.Unmarshal([]byte(project.Content), &contentObj); err != nil {
			logs.Error("解析项目内容失败:", err)
			contentObj = project.Content // 如果解析失败，返回原始字符串
		} else {
			// 调试：打印获取的内容
			logs.Info("🔍 [GoView] 获取项目内容成功")

			// 检查是否包含地图组件
			if contentMap, ok := contentObj.(map[string]interface{}); ok {
				if componentList, exists := contentMap["componentList"]; exists {
					if components, ok := componentList.([]interface{}); ok {
						for i, comp := range components {
							if compMap, ok := comp.(map[string]interface{}); ok {
								if chartConfig, exists := compMap["chartConfig"]; exists {
									if config, ok := chartConfig.(map[string]interface{}); ok {
										if key, exists := config["key"]; exists && (key == "MapBase" || key == "MapBubble") {
											logs.Info("🔍 [GoView] 获取到地图组件[%d]:", i)
											if option, exists := config["option"]; exists {
												if optionMap, ok := option.(map[string]interface{}); ok {
													if mapRegion, exists := optionMap["mapRegion"]; exists {
														logs.Info("🔍 [GoView] 地图区域配置:", mapRegion)
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	responseProject := &GoViewProjectResponse{
		ID:           project.ID,
		ProjectName:  project.ProjectName,
		State:        project.State,
		CreateTime:   project.CreateTime.Format("2006-01-02 15:04:05"),
		IndexImage:   project.IndexImage,
		CreateUserId: project.CreateUserId,
		Remarks:      project.Remarks,
		Content:      contentObj,
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"data": responseProject,
		"msg":  "获取成功",
	}
	c.ServeJSON()
}

// SaveProjectData 保存项目数据
func (c *GoViewController) SaveProjectData() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	// 从FormData中获取projectId
	projectID := c.GetString("projectId")
	if projectID == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目ID不能为空",
		}
		c.ServeJSON()
		return
	}

	o := orm.NewOrm()
	project := &pix.GoViewProject{ID: projectID}
	err = o.Read(project)
	if err != nil {
		if err == orm.ErrNoRows {
			c.Data["json"] = map[string]interface{}{
				"code": 404,
				"msg":  "项目不存在",
			}
		} else {
			logs.Error("查询项目失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "查询项目失败",
			}
		}
		c.ServeJSON()
		return
	}

	// 从FormData中获取content字段
	contentStr := c.GetString("content")
	if contentStr == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目内容不能为空",
		}
		c.ServeJSON()
		return
	}

	// 验证content是否为有效的JSON
	var contentData interface{}
	if err := json.Unmarshal([]byte(contentStr), &contentData); err != nil {
		logs.Error("解析项目内容失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目内容格式错误",
		}
		c.ServeJSON()
		return
	}

	// 调试：打印保存的内容
	logs.Info("🔍 [GoView] 保存项目内容:", contentStr)

	// 检查是否包含地图组件
	if contentMap, ok := contentData.(map[string]interface{}); ok {
		if componentList, exists := contentMap["componentList"]; exists {
			if components, ok := componentList.([]interface{}); ok {
				for i, comp := range components {
					if compMap, ok := comp.(map[string]interface{}); ok {
						if chartConfig, exists := compMap["chartConfig"]; exists {
							if config, ok := chartConfig.(map[string]interface{}); ok {
								if key, exists := config["key"]; exists && (key == "MapBase" || key == "MapBubble") {
									logs.Info("🔍 [GoView] 发现地图组件[%d]:", i)
									if option, exists := config["option"]; exists {
										if optionMap, ok := option.(map[string]interface{}); ok {
											if mapRegion, exists := optionMap["mapRegion"]; exists {
												logs.Info("🔍 [GoView] 地图区域配置:", mapRegion)
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// 更新项目内容
	project.Content = contentStr
	_, err = o.Update(project, "content", "updated_time")
	if err != nil {
		logs.Error("更新项目失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "保存项目失败",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"msg":  "保存成功",
	}
	c.ServeJSON()
}

// EditProject 修改项目基础信息
func (c *GoViewController) EditProject() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	var projectData map[string]interface{}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &projectData); err != nil {
		logs.Error("解析项目数据失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	projectID, _ := projectData["id"].(string)
	if projectID == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目ID不能为空",
		}
		c.ServeJSON()
		return
	}

	o := orm.NewOrm()
	project := &pix.GoViewProject{ID: projectID}
	err = o.Read(project)
	if err != nil {
		if err == orm.ErrNoRows {
			c.Data["json"] = map[string]interface{}{
				"code": 404,
				"msg":  "项目不存在",
			}
		} else {
			logs.Error("查询项目失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "查询项目失败",
			}
		}
		c.ServeJSON()
		return
	}

	// 更新项目信息
	updateFields := []string{"updated_time"}
	if projectName, ok := projectData["projectName"].(string); ok && projectName != "" {
		project.ProjectName = projectName
		updateFields = append(updateFields, "project_name")
	}
	if remarks, ok := projectData["remarks"].(string); ok {
		project.Remarks = remarks
		updateFields = append(updateFields, "remarks")
	}
	if indexImage, ok := projectData["indexImage"].(string); ok {
		project.IndexImage = indexImage
		updateFields = append(updateFields, "index_image")
	}

	_, err = o.Update(project, updateFields...)
	if err != nil {
		logs.Error("更新项目失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "修改项目失败",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"msg":  "修改成功",
	}
	c.ServeJSON()
}

// DeleteProject 删除项目
func (c *GoViewController) DeleteProject() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	var projectData map[string]interface{}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &projectData); err != nil {
		logs.Error("解析项目数据失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	projectID, _ := projectData["id"].(string)
	if projectID == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目ID不能为空",
		}
		c.ServeJSON()
		return
	}

	o := orm.NewOrm()
	project := &pix.GoViewProject{ID: projectID}
	err = o.Read(project)
	if err != nil {
		if err == orm.ErrNoRows {
			c.Data["json"] = map[string]interface{}{
				"code": 404,
				"msg":  "项目不存在",
			}
		} else {
			logs.Error("查询项目失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "查询项目失败",
			}
		}
		c.ServeJSON()
		return
	}

	_, err = o.Delete(project)
	if err != nil {
		logs.Error("删除项目失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "删除项目失败",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"msg":  "删除成功",
	}
	c.ServeJSON()
}

// PublishProject 修改项目发布状态
func (c *GoViewController) PublishProject() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	var projectData map[string]interface{}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &projectData); err != nil {
		logs.Error("解析项目数据失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	projectID, _ := projectData["id"].(string)
	state, _ := projectData["state"].(float64)

	if projectID == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目ID不能为空",
		}
		c.ServeJSON()
		return
	}

	o := orm.NewOrm()
	project := &pix.GoViewProject{ID: projectID}
	err = o.Read(project)
	if err != nil {
		if err == orm.ErrNoRows {
			c.Data["json"] = map[string]interface{}{
				"code": 404,
				"msg":  "项目不存在",
			}
		} else {
			logs.Error("查询项目失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "查询项目失败",
			}
		}
		c.ServeJSON()
		return
	}

	project.State = int(state)
	_, err = o.Update(project, "state", "updated_time")
	if err != nil {
		logs.Error("更新项目状态失败:", err)
		c.Data["json"] = map[string]interface{}{
			"code": 500,
			"msg":  "修改发布状态失败",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"msg":  "发布状态修改成功",
	}
	c.ServeJSON()
}

// UploadFile 文件上传
func (c *GoViewController) UploadFile() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	// GoView使用的字段名是"object"，先尝试获取object字段
	file, header, err := c.GetFile("object")
	if err != nil {
		// 如果object字段不存在，尝试file字段（兼容其他可能的调用）
		file, header, err = c.GetFile("file")
		if err != nil {
			logs.Error("获取上传文件失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 400,
				"msg":  "获取上传文件失败",
			}
			c.ServeJSON()
			return
		}
	}
	defer file.Close()

	// 生成文件名
	fileName := fmt.Sprintf("%d_%s", time.Now().Unix(), header.Filename)
	filePath := fmt.Sprintf("uploads/%s", fileName)

	// 确保uploads目录存在
	os.MkdirAll("uploads", 0755)

	// 保存文件 - 先尝试object字段，再尝试file字段
	err = c.SaveToFile("object", filePath)
	if err != nil {
		err = c.SaveToFile("file", filePath)
		if err != nil {
			logs.Error("保存文件失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "保存文件失败",
			}
			c.ServeJSON()
			return
		}
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"data": map[string]interface{}{
			"fileName": fileName,
			"fileurl":  fmt.Sprintf("/api/goview/uploads/%s", fileName), // 返回 API 路径
		},
		"msg": "上传成功",
	}
	c.ServeJSON()
}

// ServeUploadFile 提供上传文件的静态文件服务
func (c *GoViewController) ServeUploadFile() {
	// 从URL中获取文件路径
	filePath := c.Ctx.Input.Param(":splat")
	if filePath == "" {
		c.Ctx.Output.SetStatus(404)
		c.Ctx.Output.Body([]byte("File not found"))
		return
	}

	// 构建完整的文件路径
	fullPath := filepath.Join("uploads", filePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.Ctx.Output.SetStatus(404)
		c.Ctx.Output.Body([]byte("File not found"))
		return
	}

	// 提供文件服务
	c.Ctx.Output.Download(fullPath)
}

// UploadBackgroundImage 背景图片上传
func (c *GoViewController) UploadBackgroundImage() {
	// 验证token
	_, err := c.CheckToken()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code": 401,
			"msg":  "登录已失效，请重新登录",
			"data": nil,
		}
		c.ServeJSON()
		return
	}

	// 获取项目ID
	projectID := c.GetString("projectId")
	if projectID == "" {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "项目ID不能为空",
		}
		c.ServeJSON()
		return
	}

	// 获取上传的文件
	file, header, err := c.GetFile("object")
	if err != nil {
		// 尝试file字段
		file, header, err = c.GetFile("file")
		if err != nil {
			logs.Error("获取背景图片文件失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 400,
				"msg":  "获取背景图片文件失败",
			}
			c.ServeJSON()
			return
		}
	}
	defer file.Close()

	// 验证文件类型
	allowedTypes := map[string]bool{
		"image/png":  true,
		"image/jpeg": true,
		"image/jpg":  true,
		"image/gif":  true,
	}

	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		// 根据文件扩展名判断
		ext := strings.ToLower(filepath.Ext(header.Filename))
		switch ext {
		case ".png":
			contentType = "image/png"
		case ".jpg", ".jpeg":
			contentType = "image/jpeg"
		case ".gif":
			contentType = "image/gif"
		default:
			contentType = "unknown"
		}
	}

	if !allowedTypes[contentType] {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "只支持 PNG、JPG、JPEG、GIF 格式的图片",
		}
		c.ServeJSON()
		return
	}

	// 验证文件大小 (5MB)
	if header.Size > 5*1024*1024 {
		c.Data["json"] = map[string]interface{}{
			"code": 400,
			"msg":  "背景图片大小不能超过5MB",
		}
		c.ServeJSON()
		return
	}

	// 确保背景图片目录存在
	backgroundDir := "uploads/backgrounds"
	os.MkdirAll(backgroundDir, 0755)

	// 删除项目的旧背景图片
	pattern := fmt.Sprintf("%s/%s_background_*", backgroundDir, projectID)
	if matches, err := filepath.Glob(pattern); err == nil {
		for _, match := range matches {
			os.Remove(match)
			logs.Info("删除旧背景图片:", match)
		}
	}

	// 生成新的文件名
	ext := filepath.Ext(header.Filename)
	fileName := fmt.Sprintf("%s_background_%d%s", projectID, time.Now().Unix(), ext)
	filePath := fmt.Sprintf("%s/%s", backgroundDir, fileName)

	// 保存文件
	err = c.SaveToFile("object", filePath)
	if err != nil {
		err = c.SaveToFile("file", filePath)
		if err != nil {
			logs.Error("保存背景图片失败:", err)
			c.Data["json"] = map[string]interface{}{
				"code": 500,
				"msg":  "保存背景图片失败",
			}
			c.ServeJSON()
			return
		}
	}

	logs.Info("背景图片上传成功:", filePath)

	// 获取服务器配置
	serverConfig := common.GetGlobalServerConfig()

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"data": map[string]interface{}{
			"fileName": fileName,
			"fileurl":  fmt.Sprintf("/api/goview/uploads/%s", fileName), // 返回 API 路径
		},
		"msg": "背景图片上传成功",
	}
	c.ServeJSON()
}
