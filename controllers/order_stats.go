package controllers

import (
	"ccapi/models/dto"
	"ccapi/service"
	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
)

// InitOrderStatsRouter 初始化订单统计路由
func InitOrderStatsRouter() beego.LinkNamespace {
	return beego.NSNamespace("/order_stats",
		beego.NSRouter("/overview", &OrderStatsController{}, "get:GetOverviewStats"),   // 订单概览统计
		beego.NSRouter("/trend", &OrderStatsController{}, "get:GetTrendStats"),         // 订单趋势统计
		beego.NSRouter("/route", &OrderStatsController{}, "get:GetRouteStats"),         // 路线统计
		beego.NSRouter("/region", &OrderStatsController{}, "get:GetRegionStats"),       // 地区统计
		beego.NSRouter("/vehicle", &OrderStatsController{}, "get:GetVehicleStats"),     // 车辆统计
		beego.NSRouter("/user", &OrderStatsController{}, "get:GetUserStats"),           // 用户统计
		beego.NSRouter("/realtime", &OrderStatsController{}, "get:GetRealtimeStats"),   // 实时统计
		beego.NSRouter("/status_radar", &OrderStatsController{}, "get:GetStatusRadarStats"), // 订单状态雷达图统计
		beego.NSRouter("/revenue", &OrderStatsController{}, "get:GetRevenueStats"),     // 收入统计
		beego.NSRouter("/hot_routes", &OrderStatsController{}, "get:GetHotRouteStats"), // 热榜路线统计
		beego.NSRouter("/city_ranking", &OrderStatsController{}, "get:GetCityRankingStats"), // 城市排行统计
	)
}

// OrderStatsController 订单统计控制器 - 专门用于大屏数据统计
type OrderStatsController struct {
	BaseController
	service *service.OrderStatsService
}

// Prepare 初始化服务
func (c *OrderStatsController) Prepare() {
	c.service = service.NewOrderStatsService().(*service.OrderStatsService)
}

// GetOverviewStats 获取订单概览统计
// @Title 订单概览统计
// @Description 获取订单概览统计数据，包括总数、今日数据、状态分布等，适配GoView大屏组件
// @Success 200 {object} dto.Response{data=dto.OrderStatsOverview}
// @Failure 400 {object} dto.Response
// @router /overview [get]
func (c *OrderStatsController) GetOverviewStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	stats, err := c.service.GetOverviewStats(c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取订单概览统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetTrendStats 获取订单趋势统计
// @Title 订单趋势统计
// @Description 获取订单趋势统计数据，支持按日/周/月分组，适配GoView折线图组件
// @Param startDate query string false "开始日期 YYYY-MM-DD"
// @Param endDate query string false "结束日期 YYYY-MM-DD"
// @Param groupBy query string false "分组方式: day, week, month"
// @Success 200 {object} dto.Response{data=dto.OrderTrendStats}
// @Failure 400 {object} dto.Response
// @router /trend [get]
func (c *OrderStatsController) GetTrendStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.OrderTrendQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析趋势统计参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetTrendStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取订单趋势统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetRouteStats 获取路线统计
// @Title 路线统计
// @Description 获取路线统计数据，包括热门路线排行，适配GoView柱状图和表格组件
// @Param startDate query string false "开始日期 YYYY-MM-DD"
// @Param endDate query string false "结束日期 YYYY-MM-DD"
// @Param limit query int false "返回数量限制，默认10"
// @Success 200 {object} dto.Response{data=dto.RouteStats}
// @Failure 400 {object} dto.Response
// @router /route [get]
func (c *OrderStatsController) GetRouteStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.RouteStatsQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析路线统计参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetRouteStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取路线统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetRegionStats 获取地区统计
// @Title 地区统计
// @Description 获取地区统计数据，包括起点终点分布，适配GoView饼图和表格组件
// @Param startDate query string false "开始日期 YYYY-MM-DD"
// @Param endDate query string false "结束日期 YYYY-MM-DD"
// @Param type query string false "统计类型: begin, end, route"
// @Success 200 {object} dto.Response{data=dto.RegionStats}
// @Failure 400 {object} dto.Response
// @router /region [get]
func (c *OrderStatsController) GetRegionStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.RegionStatsQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析地区统计参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetRegionStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取地区统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetVehicleStats 获取车辆统计
// @Title 车辆统计
// @Description 获取车辆统计数据，包括车辆使用率排行，适配GoView柱状图和表格组件
// @Param startDate query string false "开始日期 YYYY-MM-DD"
// @Param endDate query string false "结束日期 YYYY-MM-DD"
// @Param limit query int false "返回数量限制，默认10"
// @Success 200 {object} dto.Response{data=dto.VehicleStats}
// @Failure 400 {object} dto.Response
// @router /vehicle [get]
func (c *OrderStatsController) GetVehicleStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.VehicleStatsQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析车辆统计参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetVehicleStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取车辆统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetUserStats 获取用户统计
// @Title 用户统计
// @Description 获取用户统计数据，包括用户活跃度、国家分布等，适配GoView数字卡片和饼图组件
// @Param startDate query string false "开始日期 YYYY-MM-DD"
// @Param endDate query string false "结束日期 YYYY-MM-DD"
// @Param country query string false "国家筛选"
// @Success 200 {object} dto.Response{data=dto.UserStats}
// @Failure 400 {object} dto.Response
// @router /user [get]
func (c *OrderStatsController) GetUserStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.UserStatsQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析用户统计参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetUserStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取用户统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetRealtimeStats 获取实时统计
// @Title 实时统计
// @Description 获取实时统计数据，包括当前小时数据、最近24小时趋势等，适配GoView实时大屏组件
// @Param hours query int false "查询最近几小时的数据，默认24"
// @Success 200 {object} dto.Response{data=dto.RealtimeStats}
// @Failure 400 {object} dto.Response
// @router /realtime [get]
func (c *OrderStatsController) GetRealtimeStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.RealtimeStatsQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析实时统计参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetRealtimeStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取实时统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetStatusRadarStats 获取订单状态雷达图统计
// @Title 订单状态雷达图统计
// @Description 获取订单状态雷达图统计数据，包括各状态订单分布，适配GoView雷达图组件
// @Param startDate query string false "开始日期 YYYY-MM-DD"
// @Param endDate query string false "结束日期 YYYY-MM-DD"
// @Param compareType query string false "对比类型: period(时期对比), none(无对比)"
// @Success 200 {object} dto.Response{data=dto.OrderStatusRadarStats}
// @Failure 400 {object} dto.Response
// @router /status_radar [get]
func (c *OrderStatsController) GetStatusRadarStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	var query dto.OrderStatusRadarQuery
	if err := c.ParseForm(&query); err != nil {
		logs.Error("解析订单状态雷达图参数失败: %v", err)
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	stats, err := c.service.GetStatusRadarStats(&query, c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取订单状态雷达图统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetRevenueStats 获取收入统计
// @Title 收入统计
// @Description 获取今日、本月、年度收入统计数据，只统计已核销订单（status=2），适配GoView数字卡片组件
// @Success 200 {object} dto.Response{data=dto.RevenueStats}
// @Failure 400 {object} dto.Response
// @router /revenue [get]
func (c *OrderStatsController) GetRevenueStats() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	stats, err := c.service.GetRevenueStats(c.Ctx, userInfo.Id)
	if err != nil {
		logs.Error("获取收入统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(stats)
}

// GetHotRouteStats 获取热榜路线统计
// @Title 热榜路线统计
// @Description 获取今年收入最多的5条路线数据，适配GoView热榜组件
// @Success 200 {object} dto.Response{data=dto.HotRouteStats}
// @Failure 400 {object} dto.Response
// @router /hot_routes [get]
func (c *OrderStatsController) GetHotRouteStats() {
	// 获取当前用户信息
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	// 调用服务层获取热榜统计
	result, err := c.service.GetHotRouteStats(int(userInfo.Id))
	if err != nil {
		logs.Error("获取热榜路线统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(result)
}

// GetCityRankingStats 获取城市排行统计
// @Title 城市排行统计
// @Description 获取今年收入最多的5个城市数据，适配GoView排行榜组件
// @Success 200 {object} dto.Response{data=[]dto.CityRankingItem}
// @Failure 400 {object} dto.Response
// @router /city_ranking [get]
func (c *OrderStatsController) GetCityRankingStats() {
	// 获取当前用户信息
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}

	// 调用服务层获取城市排行统计
	result, err := c.service.GetCityRankingStats(int(userInfo.Id))
	if err != nil {
		logs.Error("获取城市排行统计失败: %v", err)
		c.Fail("获取统计数据失败: " + err.Error())
		return
	}

	c.Success(result)
}
