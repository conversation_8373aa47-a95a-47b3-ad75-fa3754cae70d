# CCAPI + GoView 集成项目

## 📋 项目概述

本项目将专业的数据可视化平台 GoView 与现有的 CCAPI 管理系统进行了集成，提供完整的数据管理和可视化解决方案。

## 🏗️ 项目架构

```
ccapiex/
├── frontend/     # 主前端项目 (Vue 3 + TypeScript)
│   ├── src/
│   │   ├── views/
│   │   │   ├── HomeView.vue         # 主页 (包含GoView入口)
│   │   │   └── GoViewPortal.vue     # GoView门户页面
│   │   └── router/index.ts          # 路由配置
│   └── package.json
├── go-view/                  # GoView独立项目 (官方版本)
│   ├── src/
│   ├── package.json
│   └── README.md
├── main.go                   # Go后端服务
├── start-all.sh             # 一键启动脚本
└── README-GoView.md         # 本文档
```

## 🚀 快速开始

### 方法1: 使用一键启动脚本 (推荐)

```bash
# 启动所有服务
./start-all.sh
```

### 方法2: 手动启动

```bash
# 1. 启动主前端项目
cd ccapi-admin-frontend
npm install
npm run dev  # 端口: 3000

# 2. 启动GoView (新终端)
cd ../go-view
pnpm install
pnpm dev     # 端口: 3020
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主项目 | http://localhost:3000 | CCAPI管理系统主页 |
| GoView门户 | http://localhost:3000/goview | GoView集成页面 |
| GoView编辑器 | http://localhost:3020 | 独立的GoView应用 |

## 📊 GoView 功能特性

### 🎨 核心功能
- **拖拽式编辑**: 无需编程，拖拽即可创建数据大屏
- **丰富组件库**: 柱状图、折线图、饼图、地图等多种图表
- **实时数据**: 支持API接口、WebSocket实时数据绑定
- **多种主题**: 内置多套精美主题，支持自定义
- **响应式设计**: 自适应不同屏幕尺寸

### 📈 图表类型
- 基础图表: 柱状图、折线图、饼图、散点图
- 高级图表: 雷达图、仪表盘、漏斗图、桑基图
- 地图组件: 中国地图、世界地图、3D地球
- 装饰组件: 边框、背景、文本、图片
- 媒体组件: 视频、音频、iframe

## 🔧 集成说明

### 集成方式
本项目采用**独立部署**的集成方式：
- GoView作为独立应用运行在3020端口
- 主项目通过门户页面提供GoView的访问入口
- 两个项目保持独立，便于维护和升级

### 优势
1. **技术独立**: 各自使用最适合的技术栈
2. **版本控制**: GoView可以独立升级到最新版本
3. **性能隔离**: 互不影响，稳定性更高
4. **开发效率**: 可以并行开发和部署

## 📝 使用指南

### 1. 创建数据大屏

1. 访问主项目首页 http://localhost:3000
2. 点击 "GoView 数据可视化" 按钮
3. 在GoView门户页面点击 "打开 GoView 编辑器"
4. 开始创建您的数据可视化项目

### 2. 数据源配置

GoView支持多种数据源：

```javascript
// API数据源示例
{
  "type": "api",
  "url": "http://localhost:8080/api/chart/data",
  "method": "GET",
  "headers": {
    "Authorization": "Bearer your-token"
  }
}

// 静态数据示例
{
  "type": "static",
  "data": [
    {"name": "销售额", "value": 1200},
    {"name": "利润", "value": 800}
  ]
}
```

### 3. 与后端集成

如需与现有Go后端集成，可以添加以下API端点：

```go
// 图表数据API
func GetChartData(c *beego.Controller) {
    // 返回图表数据
    data := map[string]interface{}{
        "categories": []string{"1月", "2月", "3月"},
        "series": []int{120, 200, 150},
    }
    c.Data["json"] = data
    c.ServeJSON()
}
```

## 🛠️ 开发指南

### 添加新的数据源

1. 在Go后端添加新的API端点
2. 在GoView中配置API数据源
3. 设置数据格式转换

### 自定义主题

1. 在GoView中进入主题设置
2. 调整颜色、字体、布局等
3. 保存为自定义主题

### 组件扩展

GoView支持自定义组件开发，详见官方文档：
https://www.mtruning.club/guide/

## 🔍 故障排除

### 常见问题

1. **GoView无法访问**
   - 检查3020端口是否被占用
   - 确认GoView服务是否正常启动

2. **数据不显示**
   - 检查API接口是否正常
   - 确认数据格式是否正确
   - 查看浏览器控制台错误信息

3. **跨域问题**
   - 在Go后端添加CORS支持
   - 或使用代理配置

### 日志查看

```bash
# 查看前端日志
cd ccapi-admin-frontend && npm run dev

# 查看GoView日志
cd go-view && pnpm dev
```

## 📚 相关文档

- [GoView 官方文档](https://www.mtruning.club/guide/)
- [GoView GitHub](https://github.com/dromara/go-view)
- [Vue 3 文档](https://vuejs.org/)
- [Beego 文档](https://beego.vip/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目遵循 MIT 许可证。GoView 遵循 Apache-2.0 许可证。
