-- 测试热榜路线查询SQL
-- 这个查询会返回所有路线，包括收入为0的路线

SELECT
    r.name as route_name,
    r.city as city_name,
    COALESCE(SUM(CASE WHEN o.status = 2 AND o.created_time >= 1704067200 AND o.created_time <= 1735689599 THEN o.total_fee ELSE 0 END), 0) as total_revenue
FROM `route` r
LEFT JOIN `order` o ON r.id = o.route_id
WHERE 1=1
GROUP BY r.id, r.name, r.city
ORDER BY total_revenue DESC
LIMIT 5;

-- 说明：
-- 1. 从 route 表开始查询，确保包含所有路线
-- 2. LEFT JOIN order 表，这样即使没有订单的路线也会被包含
-- 3. 使用 CASE WHEN 条件来只计算符合条件的订单收入
-- 4. COALESCE 确保没有订单的路线收入显示为 0
-- 5. 按收入降序排列，取前5条
