import { useSystemStore } from '@/store/modules/systemStore/systemStore'
import { SystemStoreEnum } from '@/store/modules/systemStore/systemStore.d'

// * 初始化
export const useSystemInit = async () => {
  const systemStore = useSystemStore()

  // 设置固定的上传地址前缀（使用当前服务域名 + /uploads/）
  const uploadBaseUrl = `${window.location.protocol}//${window.location.host}/uploads/`

  systemStore.setItem(SystemStoreEnum.FETCH_INFO, {
    OSSUrl: uploadBaseUrl
  })
}