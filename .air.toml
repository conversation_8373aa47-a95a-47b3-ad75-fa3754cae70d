
# .air.toml - air 热重载配置文件
# 根目录，相对于此文件的位置
root = "."
# 临时目录，用于存放构建产物
tmp_dir = "tmp"

[build]
# 构建和运行应用的主入口文件
cmd = "go build -o ./tmp/main main.go"
# 运行编译后程序的可执行命令
full_bin = "chmod +x ./tmp/main && ./tmp/main"
# 发生错误时停止，不再尝试启动
stop_on_error = true

# 需要监听变化的目录
# 我选择了所有可能的 Go 源码和配置目录
include_dir = ["auth", "common", "conf", "controllers", "exporters", "migrations", "models", "pkg", "routers", "server", "service", "task"]

# 需要排除的目录，避免不必要的热重载
# 这里排除了前端项目、node_modules 和其他非 Go 业务目录
exclude_dir = [
    "rontend",
    "go-view",
    "go-view-backup",
    "node_modules",
    "tmp",
    "upload",
    "uploads",
    "scripts",
    "docs",
    "certs",
    "fonts",
    "lanelet2",
    ".git",
    ".idea"
]

# 监听的文件扩展名
include_ext = ["go", "toml", "conf"]

# 排除的文件或正则表达式
exclude_regex = ["_test.go"]

# 延迟触发重载，单位毫秒
delay = 1000

[log]
# 显示日志时间
time = true

[color]
# 自定义日志颜色
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# 退出时清理临时目录
clean_on_exit = true
